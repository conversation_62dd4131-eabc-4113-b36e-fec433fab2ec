#!D:/python37/python.exe
print ("Content-type: text/html\n")

import cx_Oracle
import mysql.connector

#koneksi mysql
db = mysql.connector.connect(
    host="**************",
    user="root",
    password="dabantek2018",
    database="monev2025",
    port="3352")
curMysql = db.cursor()

SQL_truncate ="TRUNCATE monev2025.bansos_pkh"
curMysql.execute(SQL_truncate)

SQL_isi = "INSERT INTO monev2025.bansos_pkh(jenis_transaksi,kode_lokasi,kdsatker,tgl_sp2d,tahap,return_code,jml,nilai) \
            VALUES(%s,%s,%s,%s,%s,%s,%s,%s)"

#koneksi oracle
dbOracle = cx_Oracle.Connection("USRPA/pdpsipa@**************:1521/olap23")
curOracle = dbOracle.cursor()

SQL = "SELECT jenis_transaksi,kode_lokasi,kdsatker,to_char(tgl_sp2d,'yyyy-mm-dd') as tgl_sp2d,tahap,return_code,count(no_rek) as jml, \
        sum(nilai) as nilai from bansos_pkh where TO_CHAR(tgl_sp2d,'yyyy')='2025' \
        group by jenis_transaksi,kode_lokasi,kdsatker,tgl_sp2d,tahap,return_code"
curOracle.execute(SQL)
data = curOracle.fetchall()
jml_rec = str(len(data))

count = int(1)
for row in data :
    curMysql.execute(SQL_isi,(row[0],row[1],row[2],row[3],row[4],row[5],row[6],row[7]))
 
#print(kddept,kdsatker,kdakun)

#curMysql.execute(SQL_isi,(kddept,kdunit,kdsatker,kddekon,kdprogram,kdgiat,kdoutput,kdlokasi,kdkabkota,kdakun,kdbeban,kdctarik,kdkppn,register,rupiah,nosp2d,tgsp2d,nospm,tgspm,jensp2d,jenspm,tgpos,uraian))
   
print("isi data ke-", count, "dari", jml_rec, "berhasil") 
count += int(1) 


dbOracle.close()
db.close()

